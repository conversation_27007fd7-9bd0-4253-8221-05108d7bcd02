'use client'

import { memo } from 'react'
import { Popup, PopupProps } from 'antd-mobile'
import clsx from 'clsx'

import { Close } from '../../icons'

import './style.css'

export type CustomPopupProps = Omit<PopupProps, 'children'> & {
  /** 是否显示头部 */
  showHeader?: boolean
  /** 头部标题 */
  headTitle?: string
  /** 头部左侧自定义内容 */
  headLeft?: React.ReactNode
  /** 弹窗内容 */
  children: React.ReactNode
  /** 底部内容 */
  footer?: React.ReactNode
  /** 自定义类名 */
  className?: string
  /** 自定义头部类名 */
  headerClassName?: string
  /** 自定义内容区类名 */
  contentClassName?: string
  /** 自定义底部类名 */
  footerClassName?: string
  /** 自定义遮罩层类名 */
  maskClassName?: string
  /** 关闭icon类名 */
  closeIconClassName?: string
}

const CustomPopup = ({
  visible,
  bodyStyle,
  closeIconClassName,
  position = 'bottom',
  showHeader = false,
  closeIcon = <Close />,
  headTitle = '',
  headLeft = null,
  onClose,
  children,
  footer,
  className,
  headerClassName,
  contentClassName,
  footerClassName,
  maskClassName,
  ...restProps
}: CustomPopupProps) => {
  // 处理关闭事件
  const handleClose = () => {
    if (typeof onClose === 'function') {
      onClose()
    }
  }

  return (
    <Popup
      visible={visible}
      disableBodyScroll={true}
      bodyStyle={{
        maxHeight: '60rem',
        ...bodyStyle,
        borderTopLeftRadius: '16px',
        borderTopRightRadius: '16px',
        minHeight: '20rem',
        display: 'flex',
        flexDirection: 'column' as const,
        padding: 0,
        overflow: 'hidden',
      }}
      position={position}
      closeIcon={showHeader ? null : closeIcon}
      onClose={handleClose}
      className={clsx('custom-popup', className)}
      maskClassName={clsx('custom-popup-mask blur-mask', maskClassName)}
      // closeOnMaskClick={!showHeader}
      closeOnMaskClick={true}
      {...restProps}>
      {/* 头部区域 */}
      {showHeader && (
        <header
          className={clsx(
            'mx-8 flex items-center border-b py-8',
            headerClassName,
            headLeft || headTitle ? 'justify-between' : 'justify-end',
          )}>
          {headLeft || headTitle ? (
            <div className="flex-1">
              {headLeft || (
                <h2 className="font-miSansDemiBold450 text-[18px] leading-[22px] text-[#000000]">
                  {headTitle}
                </h2>
              )}
            </div>
          ) : null}
          <button
            onClick={handleClose}
            className={clsx(
              'flex h-10 w-10 items-center justify-center rounded-full hover:bg-gray-50',
              closeIconClassName,
            )}
            aria-label="关闭弹窗">
            <Close />
          </button>
        </header>
      )}

      {/* 内容区域 */}
      <div className={clsx('flex-1 overflow-y-auto', contentClassName)}>{children}</div>

      {/* 底部区域 */}
      {footer && (
        <footer
          className={clsx(
            'border-t border-gray-100 bg-white px-base-24 pb-12 pt-base-16',
            footerClassName,
          )}>
          {footer}
        </footer>
      )}
    </Popup>
  )
}

// 添加组件显示名称
CustomPopup.displayName = 'CustomPopup'

// 使用 memo 优化性能
export default memo(CustomPopup)
