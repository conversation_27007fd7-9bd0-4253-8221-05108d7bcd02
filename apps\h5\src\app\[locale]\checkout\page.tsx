'use client'

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  PRECISE_RATE_LIMIT,
  TCheckoutCustomerCart,
  useGetCustomerCartQuery,
  useLoadingContext,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useNavigate, usePlaceOrder, useUserAddress } from '@ninebot/core/src/businessHooks'
import { DELIVERY_METHOD_TYPE, TRACK_EVENT } from '@ninebot/core/src/constants'
import {
  cartIdSelector,
  checkoutGroupedProductsSelector,
  checkoutHasInsuranceItemSelector,
  checkoutIsInitProcessCompletedSelector,
  checkoutIsNeedSetShippingAddressAndBillingAddressSelector,
  checkoutIsNeedSetShippingMethodsSelector,
  checkoutNCoinPayTotalSelector,
  checkoutPaymentMethodsSelector,
  checkoutPlaceOrderAddressIdSelector,
  checkoutPricesSelector,
  checkoutSelectedPaymentMethodSelector,
  checkoutSelectedShippingMethodSelector,
  checkoutShippingMethodsSelector,
  resetCheckout,
  setCheckoutData,
  setCheckoutInitProcessComplete,
  setCheckoutPlaceOrderAddressId,
  setCheckoutPlaceOrderPaymentMethodCode,
  setCheckoutPlaceOrderShippingMethodCode,
  setCheckoutUserNCoinInfo,
  storeConfigSelector,
} from '@ninebot/core/src/store'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import isUndefined from 'lodash-es/isUndefined'

// import { parseToKeyValueObject } from '@ninebot/core/src/utils'
// import { Dialog } from 'antd-mobile'
// import isUndefined from 'lodash-es/isUndefined'
import { CustomNavBar, Header, Skeleton } from '@/components'
import { CartItem } from '@/types/checkout'

import {
  AddressCard,
  BottomActionBar,
  InsuranceFormCard,
  InvoiceSection,
  PaymentSection,
  PriceSummary,
  ProductTypeList,
} from './components'

// import { PickUpProductsCard } from './components/pickUpProductsCard'

// 定义验证结果类型
type ValidationResult = string | { [key: string]: string | number | boolean }

// 定义验证器接口
interface Validator {
  validate: () => Promise<ValidationResult | null>
  scrollTo: () => void
}

// 定义验证引用类型
interface ValidatorRef {
  [key: string]: Validator
}

const CheckoutPage = () => {
  // const pageParams = parseToKeyValueObject(props?.route?.params?.pageParams)
  const dispatch = useAppDispatch()
  const toast = useToastContext()
  const loading = useLoadingContext()
  const getI18nString = useTranslations('Common')
  const { reportEvent } = useVolcAnalytics()
  const { openPage } = useNavigate()
  // const { top: safeAreaTop, bottom: safeAreaBottom } = useSafeAreaInsets()

  const groupedProducts = useAppSelector(checkoutGroupedProductsSelector)
  const cartId = useAppSelector(cartIdSelector)
  const shippingMethods = useAppSelector(checkoutShippingMethodsSelector)
  const paymentMethods = useAppSelector(checkoutPaymentMethodsSelector)
  const placeOrderAddressId = useAppSelector(checkoutPlaceOrderAddressIdSelector)
  const isNeedSetShippingAddressAndBillingAddress = useAppSelector(
    checkoutIsNeedSetShippingAddressAndBillingAddressSelector,
  )
  const isNeedSetShippingMethods = useAppSelector(checkoutIsNeedSetShippingMethodsSelector)
  const selectedShippingMethod = useAppSelector(checkoutSelectedShippingMethodSelector)
  const selectedPaymentMethod = useAppSelector(checkoutSelectedPaymentMethodSelector)
  const hasInsuranceItem = useAppSelector(checkoutHasInsuranceItemSelector)
  const isInitProcessCompleted = useAppSelector(checkoutIsInitProcessCompletedSelector)
  const prices = useAppSelector(checkoutPricesSelector)
  const NCoinPayTotal = useAppSelector(checkoutNCoinPayTotalSelector)

  const scrollViewRef = useRef<HTMLDivElement>(null)
  const validatorRef = useRef<ValidatorRef>({})
  const [showContent, setShowContent] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const storeConfig = useAppSelector(storeConfigSelector)
  // 控制只上报一次曝光事件
  const reportFlag = useRef(false)

  const { fetchUserAddresses, userAddressDefault, haveUserAddresses, firstUserAddress } =
    useUserAddress()
  const {
    handleSetShippingAddressesOnCart,
    handleSetBillingAddressOnCart,
    handleSetShippingMethodsOnCart,
  } = usePlaceOrder()
  const {
    currentData: customerCartData,
    isError,
    error,
  } = useGetCustomerCartQuery({
    currentPage: 1,
    pageSize: 1000,
  })

  /**
   * 是否展示支付方式
   */
  const isShowPayment = useMemo(() => {
    return selectedPaymentMethod && selectedPaymentMethod?.code !== 'free'
  }, [selectedPaymentMethod])

  /**
   * 骨架屏处理
   */
  useEffect(() => {
    if (isInitProcessCompleted) {
      timeoutRef.current = setTimeout(() => {
        setShowContent(true)
      }, 15)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [isInitProcessCompleted])

  /**
   * 错误提示
   */
  useEffect(() => {
    if (isError && error) {
      // RTK Query 错误结构检查
      const errorType = 'type' in error ? error.type : undefined
      console.log('H5 Checkout page error:', {
        error,
        errorType,
        isRateLimit: errorType === PRECISE_RATE_LIMIT,
      })

      // 检查是否为 427/428 限流错误
      if (errorType === PRECISE_RATE_LIMIT) {
        console.log('Rate limit detected, redirecting to home in 3 seconds')
        // 显示 toast 提示
        const errorMessage = 'data' in error ? String(error.data) : '当前访问人数过多，请稍后再试！'
        toast.show({
          icon: 'fail',
          content: errorMessage,
        })

        // 3 秒后重定向到首页
        setTimeout(() => {
          console.log('Redirecting to home page due to rate limit')
          openPage({
            route: 'home',
            replace: true,
          })
        }, 3000)
      } else {
        // 其他错误正常显示 toast
        const errorMessage =
          'data' in error ? String(error.data) : getI18nString('fetch_data_error')
        toast.show({
          icon: 'fail',
          content: errorMessage,
        })
      }
    }
  }, [toast, isError, error, getI18nString, openPage])

  /**
   * 保存 checkout 数据
   */
  useEffect(() => {
    if (customerCartData?.customerCart) {
      dispatch(setCheckoutData(customerCartData.customerCart))
    }
    if (customerCartData?.check_nb_info) {
      dispatch(setCheckoutUserNCoinInfo(customerCartData.check_nb_info))
    }
  }, [customerCartData, dispatch])

  /**
   * 每次进入结算页，都获取最新地址信息
   */
  useEffect(() => {
    fetchUserAddresses()
  }, [fetchUserAddresses])

  /**
   * 如果没有下单地址 ID，优先使用默认地址 ID 或地址簿中的第 1个地址 ID
   */
  useEffect(() => {
    if (!placeOrderAddressId && userAddressDefault?.address_id) {
      dispatch(setCheckoutPlaceOrderAddressId(userAddressDefault.address_id))
    } else if (!placeOrderAddressId && firstUserAddress?.address_id) {
      dispatch(setCheckoutPlaceOrderAddressId(firstUserAddress.address_id))
    }
  }, [dispatch, userAddressDefault, placeOrderAddressId, firstUserAddress])

  /**
   * 结算流程 1，默认选中 shipping method & payment method
   */
  useEffect(() => {
    if (isNeedSetShippingAddressAndBillingAddress) {
      // 设置默认的 shipping method
      if (shippingMethods?.length > 0 && !selectedShippingMethod) {
        // 获取第一个 shipping method
        const [firstShippingMethod] = shippingMethods
        dispatch(setCheckoutPlaceOrderShippingMethodCode(firstShippingMethod?.method_code))
      }
    }

    // 设置默认的 payment method
    if (paymentMethods?.length > 0 && !selectedPaymentMethod) {
      // 获取第一个 payment method
      const [firstPaymentMethod] = paymentMethods
      dispatch(setCheckoutPlaceOrderPaymentMethodCode(firstPaymentMethod?.code))
    }
  }, [
    dispatch,
    paymentMethods,
    shippingMethods,
    isNeedSetShippingAddressAndBillingAddress,
    selectedShippingMethod,
    selectedPaymentMethod,
  ])

  /**
   * 结算流程 2
   */
  useEffect(() => {
    const initCheckoutProcess = async () => {
      if (!cartId) {
        return
      }

      // 需要设置 shipping address and billing address
      if (
        isNeedSetShippingAddressAndBillingAddress &&
        placeOrderAddressId &&
        isNeedSetShippingMethods &&
        selectedShippingMethod
      ) {
        loading.show()
        // 同时设置 shipping address and billing address
        const setAddressResult = await Promise.all([
          handleSetShippingAddressesOnCart({
            cart_id: cartId,
            shipping_addresses: [{ customer_address_id: Number(placeOrderAddressId) }],
          }),
          handleSetBillingAddressOnCart({
            cart_id: cartId,
            billing_address: {
              customer_address_id: Number(placeOrderAddressId),
            },
          }),
        ])

        if (setAddressResult.every(Boolean)) {
          // 设置 shipping methods
          const setShippingMethodsResult = await handleSetShippingMethodsOnCart({
            cart_id: cartId,
            shipping_methods: [
              {
                carrier_code: selectedShippingMethod.carrier_code || '',
                method_code: selectedShippingMethod.method_code || '',
              },
            ],
          })

          // 根据返回结果，更新 prices
          if (setShippingMethodsResult?.cart?.id) {
            // 类型转换，确保符合Cart类型
            const cartData = setShippingMethodsResult.cart as TCheckoutCustomerCart
            dispatch(setCheckoutData(cartData))
            // 完成结算流程
            dispatch(setCheckoutInitProcessComplete(true))
          }
        }
        loading.hide()
        return
      }

      // 不需要设置 shipping address and billing address
      if (!isNeedSetShippingAddressAndBillingAddress || !firstUserAddress) {
        // 完成结算流程
        dispatch(setCheckoutInitProcessComplete(true))
      }
    }

    initCheckoutProcess()
  }, [
    dispatch,
    placeOrderAddressId,
    cartId,
    handleSetShippingAddressesOnCart,
    handleSetBillingAddressOnCart,
    isNeedSetShippingAddressAndBillingAddress,
    handleSetShippingMethodsOnCart,
    isNeedSetShippingMethods,
    selectedShippingMethod,
    loading,
    firstUserAddress,
  ])

  /**
   * 点击返回按钮
   */
  const handleNavBarLeftPress = useCallback(() => {
    dispatch(resetCheckout())

    return true
  }, [dispatch])

  // 埋点：商品结算
  useEffect(() => {
    if (
      !isUndefined(prices?.grand_total?.value) &&
      !isUndefined(NCoinPayTotal?.grand_total) &&
      !reportFlag.current
    ) {
      reportFlag.current = true
      reportEvent(TRACK_EVENT.shop_commodity_settlement_exposure, {
        cash_amount: prices.grand_total.value || 0,
        N_amount: NCoinPayTotal.grand_total || 0,
      })
    }
  }, [prices, NCoinPayTotal, reportEvent])

  return (
    <>
      <Header isCheckout />
      {/* 顶部导航 */}
      <CustomNavBar title={getI18nString('shopping_checkout')} onBack={handleNavBarLeftPress} />
      {showContent ? (
        <div
          className="pb-safe-bottom-bar flex min-h-screen flex-col gap-base bg-[#F8F8F9] px-base-12 pt-base"
          ref={scrollViewRef}>
          {/* 配送地址 */}
          {isNeedSetShippingAddressAndBillingAddress && (
            <AddressCard haveUserAddresses={haveUserAddresses} />
          )}

          {/* 保单表单组件 */}
          {hasInsuranceItem ? (
            <InsuranceFormCard
              ref={(ref) => {
                if (validatorRef.current && ref) {
                  validatorRef.current.insurance_information = ref
                }
              }}
              scrollViewRef={scrollViewRef}
            />
          ) : null}

          {/* 自提 / 普通商品列表 */}
          {groupedProducts.map((group) => {
            if (DELIVERY_METHOD_TYPE.store_pickup === group.code) {
              return (
                <ProductTypeList
                  key={group.code}
                  showType="pickup"
                  ref={(ref) => {
                    if (validatorRef.current && ref) {
                      validatorRef.current[group.code] = ref
                    }
                  }}
                  scrollViewRef={scrollViewRef}
                  title={group.title}
                  products={group.products as unknown as CartItem[]}
                />
              )
            }

            return (
              <ProductTypeList
                key={group.code}
                title={group.title}
                products={group.products as unknown as CartItem[]}
              />
            )
          })}

          {/* 价格明细 */}
          <PriceSummary />

          {/* 发票信息 */}
          {storeConfig?.enable_checkout_invoice && <InvoiceSection />}

          {/* 支付方式 */}
          {isShowPayment ? <PaymentSection /> : null}
        </div>
      ) : (
        <div className="flex flex-col bg-gray-50 px-base-12 pb-20">
          <Skeleton
            style={{
              borderRadius: 12,
              height: 90,
              width: '100%',
              marginTop: 12,
            }}
          />
          {new Array(2).fill(null).map((_, index) => (
            <Skeleton
              key={index}
              style={{
                borderRadius: 12,
                height: 200,
                width: '100%',
                marginTop: 12,
              }}
            />
          ))}
          <Skeleton
            style={{
              borderRadius: 12,
              height: 90,
              width: '100%',
              marginTop: 12,
            }}
          />
        </div>
      )}

      {/* 底部操作栏 */}
      {showContent ? <BottomActionBar validatorRef={validatorRef} /> : null}
    </>
  )
}

export default CheckoutPage
