'use client'

import { useRef, useState } from 'react'
import { Button } from 'antd-mobile'

import { QuantitySelector } from '@/businessComponents'
import { CustomPopup } from '@/components'
import { useVaulKeyboardFix } from '@/hooks/useVaulKeyboardFix'

export default function TestKeyboardPage() {
  const [visible, setVisible] = useState(false)
  const popupRef = useRef<HTMLDivElement>(null)

  // 应用键盘修复
  useVaulKeyboardFix(popupRef, true)

  return (
    <div className="p-4">
      <h1 className="mb-4 text-xl font-bold">iOS键盘适配测试</h1>

      <Button color="primary" onClick={() => setVisible(true)} className="mb-4">
        打开测试弹窗
      </Button>

      <div className="text-sm text-gray-600">
        <p>测试步骤：</p>
        <ol className="list-inside list-decimal space-y-1">
          <li>点击上方按钮打开弹窗</li>
          <li>点击数量输入框</li>
          <li>观察弹窗是否正确适配键盘</li>
          <li>输入框应该保持可见</li>
        </ol>
      </div>

      <div ref={popupRef}>
        <CustomPopup
          showHeader
          visible={visible}
          onClose={() => setVisible(false)}
          headTitle="键盘适配测试"
          bodyStyle={{
            backgroundColor: '#F3F3F4',
            maxHeight: '65rem',
          }}
          footer={
            <Button color="primary" className="w-full" onClick={() => setVisible(false)}>
              确认
            </Button>
          }>
          <div className="space-y-6 p-4">
            <div>
              <label className="mb-2 block text-sm font-medium">项目名称</label>
              <input
                type="text"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入项目名称"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">数量选择</label>
              <QuantitySelector
                value={1}
                inputMax={99}
                onQtyChange={(value, max) => console.log('数量变化:', value, max)}
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">描述</label>
              <textarea
                rows={4}
                className="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入描述信息"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">邮箱</label>
              <input
                type="email"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入邮箱地址"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">电话</label>
              <input
                type="tel"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入电话号码"
              />
            </div>
          </div>
        </CustomPopup>
      </div>
    </div>
  )
}
