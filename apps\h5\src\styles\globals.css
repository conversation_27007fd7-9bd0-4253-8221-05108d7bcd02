/* stylelint-disable at-rule-no-unknown */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 默认值 */
  --nb-font-size: 10px;
  --adm-font-family: var(--font-family-miSansMedium380), 'Microsoft Yahei', sans-serif;
}

html {
  font-size: var(--nb-font-size, 10px);
  transition: font-size 0.2s ease-in-out;
  text-size-adjust: 100%; /* 禁用自动字体调整 */
  touch-action: manipulation;
}

/* #app必须保留 */
#app {
  height: 100%;
}

/*========= 自定义antd-mobile组件样式 =========*/
@import url('./components/tabs.css');
@import url('./components/sidebar.css');
@import url('./components/dropdown.css');
@import url('./components/checkoutlist.css');
@import url('./components/form.css');
@import url('./components/checkbox.css');
@import url('./components/dialog.css');
@import url('./components//imageuploader.css');
@import url('./components/card.css');
@import url('./components/collapse.css');
@import url('./components/imageviewer.css');
@import url('./components/SwipeAction.css');
@import url('./components/button.css');
@import url('./components/navbar.css');

/*========= 自定义样式 =========*/

.max-container {
  width: 100%;
  max-width: 375px;
  margin: 0 auto;
  box-sizing: border-box;
}

.scroll-container::-webkit-scrollbar {
  height: 0; /* 隐藏水平滚动条 */
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: transparent; /* 可选：设置滑块为透明，确保不会显示 */
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

.search-input .adm-input-clear {
  padding: 0.8rem;
}

.adm-badge .adm-badge-content {
  font-size: 1.2rem;
}

.adm-error-block .adm-error-block-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.adm-stepper-middle {
  border-left: 1px solid #f3f3f4 !important;
  border-right: 1px solid #f3f3f4 !important;
}

.adm-stepper-minus:disabled,
.adm-stepper-plus:disabled {
  background: #fff;
}

.adm-error-block .adm-error-block-description {
  margin-top: 0px !important;
}

.adm-error-block .adm-error-block-description-title {
  display: none;
}

.coupon-desc-content p {
  font-size: 14px;
  font-family: var(--font-family-miSansRegular330);
  line-height: 1.6;
  margin-bottom: 20px;
  color: #6e6e73;

  &:last-child {
    margin-bottom: 0;
  }
}

/*========= 移动端安全区域适配 =========*/

/* 底部安全区域适配 - 用于固定底部操作栏的页面 */
@supports (padding: max(0px)) {
  .pb-safe-bottom-bar {
    padding-bottom: max(76px, calc(68px + env(safe-area-inset-bottom)));
  }

  .pb-safe {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .pt-safe {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
}
