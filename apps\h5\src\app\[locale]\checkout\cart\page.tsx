'use client'

import { useEffect, useState } from 'react'
import { TRACK_EVENT, useAuth, useVolcAnalytics } from '@ninebot/core'
import { PullToRefresh } from 'antd-mobile'

import { Header, RecommendedProducts, Skeleton } from '@/components'
import { Arrow } from '@/components'

import { CartProvider, useCart } from './context/cartContext'
import {
  BottomActionBar,
  CartEmpty,
  CartHeaderBar,
  CartItem,
  CartNavTabs,
  ProductOptionsPopup,
} from './components'
/**
 * 购物车页面
 */
const CartContent = () => {
  const { showContent, cartAllProductsTotal, cartFilteredProducts, isEditMode, handleRefresh } =
    useCart()

  const { redirectLoginUrl, isLoggedIn } = useAuth()
  const { reportEvent } = useVolcAnalytics()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // 埋点：购物车页曝光
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_cart_page_exposure)
  }, [reportEvent])

  return (
    <>
      <div className="bg-gray-base">
        <Header />
        {/* 顶部导航 */}
        <CartHeaderBar />

        {/* 未登录提示 */}
        {mounted && !isLoggedIn && (
          <div className="flex items-center justify-between bg-[#FEF2F2] px-base-12 py-base text-sm text-primary">
            <div>登录后享受更多优惠</div>
            <div className="flex items-center gap-2" onClick={() => redirectLoginUrl()}>
              <span>去登录</span>
              <Arrow size={16} color="#DA291C" />
            </div>
          </div>
        )}

        {showContent ? (
          <>
            {cartAllProductsTotal ? (
              <>
                {/* 标签页 */}
                <CartNavTabs />
                <div className={isEditMode ? 'pb-safe-bottom-bar' : ''}>
                  <PullToRefresh onRefresh={handleRefresh}>
                    <div className="overflow-hidden rounded-t-3xl bg-white px-base-12 py-8">
                      {/* 商品列表 */}
                      {cartFilteredProducts.length ? (
                        <div className="space-y-8">
                          {cartFilteredProducts.map((item, index) => (
                            <CartItem
                              key={item.uid}
                              id={item.uid}
                              isLast={index === cartFilteredProducts.length}
                            />
                          ))}
                        </div>
                      ) : (
                        <CartEmpty />
                      )}
                    </div>
                  </PullToRefresh>
                </div>

                {/* 底部操作栏 */}
                <BottomActionBar />
              </>
            ) : (
              <CartEmpty />
            )}

            {/* 推荐商品 */}
            {!isEditMode && (
              <div className="pb-safe-bottom-bar pt-[12px]">
                <div className="bg-white px-base-16 pb-8 pt-[1px]">
                  <RecommendedProducts isCartPage pageType="cart" />
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="">
            <div className="flex justify-around p-base-12">
              {new Array(4).fill(null).map((_, index) => (
                <Skeleton
                  key={index}
                  style={{
                    borderRadius: 12,
                    height: 20,
                    width: '20%',
                  }}
                />
              ))}
            </div>
            <div className="bg-white p-base-12">
              {new Array(5).fill(null).map((_, index) => (
                <div key={index} className="flex border-b pb-[12px]">
                  <Skeleton
                    key={index}
                    style={{
                      borderRadius: 8,
                      height: 88,
                      width: '88px',
                      marginTop: 12,
                    }}
                  />
                  <div className="ml-[8px] flex flex-1 flex-col">
                    {new Array(3).fill(null).map((_, index) => (
                      <Skeleton
                        key={index}
                        style={{
                          borderRadius: 8,
                          height: 20,
                          width: '100%',
                          marginTop: 12,
                        }}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <ProductOptionsPopup />
    </>
  )
}

const CartPage = () => {
  return (
    <CartProvider>
      <CartContent />
    </CartProvider>
  )
}

export default CartPage
