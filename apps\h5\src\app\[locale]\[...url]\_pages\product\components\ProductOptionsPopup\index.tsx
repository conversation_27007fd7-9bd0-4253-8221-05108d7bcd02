'use client'

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import type { ProductDetailsData, StoreListItem } from '@ninebot/core'
import { useLazyGetProductLimitQuery } from '@ninebot/core'
import { isProductConfigurable } from '@ninebot/core/src/utils/productUtils'
import { Button } from 'antd-mobile'

import {
  ColorSelector,
  CurrentPrice,
  InstallationService,
  QuantitySelector,
  SizeSelector,
  StorePickup,
} from '@/businessComponents'
import { CustomPopup, CustomTag } from '@/components'
import { CustomImage } from '@/components/common'
// import { useVaulKeyboardFix } from '@/hooks/useVaulKeyboardFix'
import type {
  Price,
  ProductAttribute,
  ProductBasic,
  ProductConfigurableOption,
  ProductDetails,
  ProductDropDownItem,
  ProductLimitData,
  ProductOptionItem,
  ProductServiceParams,
  ProductStatus,
  SafeguardItem,
} from '@/types/product'

import { useProduct } from '../../context/ProductContext'

interface ProductOptionsPopupProps {
  visible: boolean
  onClose: () => void
}

const ProductOptionsPopup = ({ visible, onClose }: ProductOptionsPopupProps) => {
  const popupRef = useRef<HTMLDivElement>(null)

  // iOS键盘适配 - 使用vaul风格的修复
  // useVaulKeyboardFix(popupRef, true)

  const {
    productDetails,
    setVisibleAddCartPop,
    safeguardItems,
    productConfigOptions,
    variants,
    productConfigurableOptions,
    handleUpdateService,
    deliveryMethodPickup,
    selectStore,
    isBuy,
    handleSelectionChange,
    handleQtyChange,
    setDoorVisible,
    buyNowLoading,
    onAddToCart,
    addCartLoading,
    productStatus,
    isLoggedIn,
    addToCartRateLimit,
    buyNowRateLimit,
  } = useProduct() as {
    productDetails: ProductDetails
    setVisibleAddCartPop: (visible: boolean) => void
    safeguardItems: SafeguardItem[]
    productConfigOptions: {
      option_id: string
      title: string
      dropDown: ProductDropDownItem[]
    }[]
    variants: {
      attributes: ProductAttribute[]
      product: ProductBasic
      uid: string
    }[]
    productConfigurableOptions: ProductConfigurableOption[]
    handleUpdateService: (params: ProductServiceParams) => void
    deliveryMethodPickup: boolean
    selectStore: StoreListItem | null
    isBuy: boolean
    handleSelectionChange: (item: ProductOptionItem, id: string | number) => void
    handleQtyChange: (qty: number, max: number) => void
    setDoorVisible: (visible: boolean) => void
    buyNowLoading: boolean
    onAddToCart: (type: number) => void
    addCartLoading: boolean
    productStatus: ProductStatus
    isLoggedIn: boolean
    addToCartRateLimit: {
      isDisabled: boolean
      buttonText: string
    }
    buyNowRateLimit: {
      isDisabled: boolean
      buttonText: string
    }
  }

  const [limitCount, setLimitCount] = useState<number | null>(null)

  const [getProductLimit] = useLazyGetProductLimitQuery()

  const { id: selectedProductId } = productStatus as ProductStatus
  const price = (productStatus as ProductStatus).price
  const paymentNcoin = (productStatus as ProductStatus).paymentNcoin || false
  const servicePrice = (productStatus as ProductStatus).servicePrice || 0
  const storePrice = selectStore ? (selectStore as StoreListItem).price : 0
  // PDP页面默认最大购买数量限制为99，即使后端返回更大的库存数量
  const salableQty = Math.min((productStatus as ProductStatus).salable_qty || 99, 99)
  const isPromote = price ? price.final_price.value !== price.regular_price.value : false

  const handleViewStorePop = () => {
    // 关闭加购弹框 打开选择门店弹框（只能同时打开一个弹框）
    setVisibleAddCartPop(false)
    setDoorVisible(true)
    // 下次打开选择规格弹框
    // appStorage.setItem('select_product_option_open', 1)
  }

  /**
   * 最大购买数量
   */
  const inputMax = useMemo(() => {
    return limitCount ? Math.min(limitCount, salableQty) : salableQty
  }, [limitCount, salableQty])

  /**
   * 获取限购数量
   */
  const getLimitCount = useCallback((data: ProductLimitData) => {
    const now = new Date().getTime()
    if (
      data?.limit &&
      now / 1000 >= (data?.start_time || 0) &&
      now / 1000 <= (data?.end_time || 0)
    ) {
      setLimitCount(Number(data?.limit_qty))
    } else {
      setLimitCount(null)
    }
  }, [])

  /**
   * 获取产品限购信息
   */
  useEffect(() => {
    if (isLoggedIn && selectedProductId) {
      getProductLimit({
        input: { product_id: Number(selectedProductId) },
      })
        .unwrap()
        .then((res) => {
          getLimitCount(res?.product_purchase_limit as ProductLimitData)
        })
        .catch((error) => {
          console.log('error', error)
        })
    }
  }, [selectedProductId, getProductLimit, getLimitCount, isLoggedIn])

  const ProductInfo = useMemo(() => {
    return (
      (productStatus as ProductStatus).image && (
        <div className="flex items-center gap-4">
          <CustomImage
            src={(productStatus as ProductStatus).image!.url}
            alt="商品图片"
            width={60}
            height={60}
            fit="cover"
            className="rounded-lg bg-[#f3f3f4]"
          />
          <div className="flex flex-col justify-between gap-base">
            <CurrentPrice
              price={price as Price}
              paymentNcoin={paymentNcoin}
              servicePrice={servicePrice}
              storePrice={storePrice}
              isPromote={isPromote}
            />
            {(safeguardItems as SafeguardItem[]).length > 0 && (
              <div className="flex flex-wrap items-center gap-2">
                {(safeguardItems as SafeguardItem[]).map(
                  (clause: SafeguardItem) =>
                    clause.value && <CustomTag key={clause.value} text={clause.label} />,
                )}
              </div>
            )}
          </div>
        </div>
      )
    )
  }, [productStatus, price, paymentNcoin, servicePrice, storePrice, isPromote, safeguardItems])

  const FooterButton = () => {
    // 根据当前操作类型获取对应的限流状态
    const currentRateLimit = isBuy ? buyNowRateLimit : addToCartRateLimit

    return (
      <Button
        color="primary"
        onClick={() => (onAddToCart as (type: number) => void)(isBuy ? 2 : 1)}
        disabled={
          addCartLoading ||
          buyNowLoading ||
          productStatus?.isEverythingOutOfStock ||
          currentRateLimit.isDisabled // 添加限流状态检查
        }
        className={`nb-button w-full ${currentRateLimit.isDisabled ? 'nb-button-no-padding' : ''}`}>
        {currentRateLimit.buttonText || (isBuy ? '立即购买' : '加入购物车')}
      </Button>
    )
  }

  return (
    <div ref={popupRef}>
      <CustomPopup
        showHeader
        visible={visible}
        onClose={onClose}
        bodyStyle={{
          backgroundColor: '#F3F3F4',
          maxHeight: '65rem',
        }}
        headerClassName="py-[12px] border-none"
        headLeft={ProductInfo}
        closeIconClassName="absolute right-[15px] top-[12px]"
        footer={<FooterButton />}>
        <div className="flex flex-col gap-base px-base-12 pb-base-12">
          {/* 商品选项 */}
          <div className="flex flex-col gap-12 rounded-base-12 bg-white px-base-12 py-base-16">
            {isProductConfigurable(productDetails as unknown as ProductDetailsData) &&
              (productConfigurableOptions as ProductConfigurableOption[])?.map(
                (option: ProductConfigurableOption) => (
                  <div key={option.attribute_id}>
                    <div className="mb-base-12 font-miSansDemiBold450 text-base">
                      {option.label}
                    </div>
                    {option.attribute_code.includes('color') ? (
                      <ColorSelector
                        optionItems={option.values}
                        id={option.attribute_id}
                        variants={variants}
                        productStatus={productStatus}
                        onSelectionChange={handleSelectionChange}
                      />
                    ) : (
                      <SizeSelector
                        optionItems={option.values}
                        id={option.attribute_id}
                        productStatus={productStatus}
                        onSelectionChange={handleSelectionChange}
                      />
                    )}
                  </div>
                ),
              )}

            {/* 安装服务 */}
            <InstallationService
              productConfigOptions={productConfigOptions}
              productStatus={productStatus}
              handleUpdateService={handleUpdateService}
            />

            {/* 数量选择 */}
            <div className="flex items-center justify-between">
              <div className="font-miSansDemiBold450 text-base">数量</div>
              <QuantitySelector
                limitCount={limitCount}
                inputMax={inputMax}
                onQtyChange={handleQtyChange}
                disabled={productStatus?.isEverythingOutOfStock}
              />
            </div>

            {/* 门店选择 */}
            {deliveryMethodPickup && (
              <div className="flex flex-col gap-base-12">
                <div className="font-miSansDemiBold450 text-base">选择门店</div>
                <StorePickup selectedStore={selectStore} handleViewStorePop={handleViewStorePop} />
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="font-miSansDemiBold450 text-base">数量</div>
              <QuantitySelector
                limitCount={limitCount}
                inputMax={inputMax}
                onQtyChange={handleQtyChange}
                disabled={productStatus?.isEverythingOutOfStock}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="font-miSansDemiBold450 text-base">数量</div>
            <QuantitySelector
              limitCount={limitCount}
              inputMax={inputMax}
              onQtyChange={(value, max) => console.log('数量变化:', value, max)}
              disabled={productStatus?.isEverythingOutOfStock}
            />
          </div>
        </div>
      </CustomPopup>
    </div>
  )
}

export default ProductOptionsPopup
