'use client'
import { useEffect, useRef } from 'react'

/**
 * 检测是否为输入元素
 */
function isInput(element: Element | null): boolean {
  if (!element) return false
  const inputTypes = ['input', 'textarea', 'select']
  const tagName = element.tagName?.toLowerCase?.() || ''

  if (inputTypes.includes(tagName)) {
    return true
  }

  // 检查是否为可编辑元素
  if ((element as HTMLElement).getAttribute?.('contenteditable') === 'true') {
    return true
  }

  return false
}

/**
 * 在容器内寻找真正的弹窗面板元素（优先 role=dialog，其次 .adm-popup-body），找不到则回退到容器本身
 */
function resolvePanelEl(container: HTMLElement): HTMLElement {
  const byRole = container.querySelector('[role="dialog"]') as HTMLElement | null
  if (byRole) return byRole
  const byClass = container.querySelector('[class*="adm-popup-body"]') as HTMLElement | null
  if (byClass) return byClass
  const byPopupRoot = container.querySelector('[class*="adm-popup"]') as HTMLElement | null
  if (byPopupRoot) return byPopupRoot
  return container
}

/**
 * Vaul风格的键盘修复Hook
 * 基于vaul的repositionInputs实现，专门处理iOS键盘弹出问题
 *
 * @param drawerRef 弹窗容器ref（建议通过 Popup 的 getContainer 挂载到该容器中）
 * @param repositionInputs 是否启用输入框重定位逻辑
 * @param fixed true 时仅收缩高度，不上移（参考 vaul 的 fixed 行为）
 */
export default function useVaulKeyboardFix(
  drawerRef: React.RefObject<HTMLElement>,
  repositionInputs: boolean = true,
  fixed: boolean = false,
) {
  const keyboardIsOpen = useRef(false)
  const previousDiffFromInitial = useRef(0)
  const initialDrawerHeight = useRef(0)

  useEffect(() => {
    function onVisualViewportChange() {
      const container = drawerRef.current
      if (!container || !repositionInputs) return

      const panelEl = resolvePanelEl(container)

      const focusedElement = document.activeElement as HTMLElement | null
      if (isInput(focusedElement) || keyboardIsOpen.current) {
        const visualViewportHeight = window.visualViewport?.height || 0
        const totalHeight = window.innerHeight
        // 键盘高度
        const diffFromInitial = totalHeight - visualViewportHeight
        const rect = panelEl.getBoundingClientRect()
        const drawerHeight = rect.height || 0
        // 只有当抽屉足够高时才调整高度
        const isTallEnough = drawerHeight > totalHeight * 0.8

        if (!initialDrawerHeight.current) {
          initialDrawerHeight.current = drawerHeight
        }
        const offsetFromTop = rect.top

        // visualViewport 高度可能因为键盘细微变化而改变。变化超过60像素认为键盘开关状态改变。
        if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {
          keyboardIsOpen.current = !keyboardIsOpen.current
        }

        previousDiffFromInitial.current = diffFromInitial

        // 当抽屉高度大于可视视口或键盘打开时，调整高度
        if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {
          const height = panelEl.getBoundingClientRect().height
          let newDrawerHeight = height

          if (height > visualViewportHeight) {
            newDrawerHeight = visualViewportHeight - (isTallEnough ? offsetFromTop : 16) // 16px 顶部偏移兜底
          }

          // 参考 vaul 的 fixed 模式实现
          if (fixed) {
            // fixed 模式：仅收缩高度，不上移
            panelEl.style.height = `${height - Math.max(diffFromInitial, 0)}px`
          } else {
            // 非 fixed 模式：调整高度并上移
            panelEl.style.height = `${Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)}px`
          }
        } else {
          // 恢复初始高度
          panelEl.style.height = `${initialDrawerHeight.current}px`
        }

        // 调整底部位置以避免键盘遮挡
        // 参考 vaul：键盘打开时跟随键盘上移
        panelEl.style.bottom = `${Math.max(diffFromInitial, 0)}px`
      } else {
        // 未聚焦输入时，重置可能的样式
        const container = drawerRef.current
        if (container) {
          const panelEl = resolvePanelEl(container)
          if (initialDrawerHeight.current) {
            panelEl.style.height = `${initialDrawerHeight.current}px`
          }
          panelEl.style.bottom = '0px'
        }
      }
    }

    window.visualViewport?.addEventListener('resize', onVisualViewportChange)
    return () => window.visualViewport?.removeEventListener('resize', onVisualViewportChange)
  }, [drawerRef, repositionInputs, fixed])

  return {
    keyboardIsOpen: keyboardIsOpen.current,
  }
}
