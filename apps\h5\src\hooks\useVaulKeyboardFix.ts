'use client'
import { useEffect, useRef } from 'react'

/**
 * 检测是否为输入元素
 */
function isInput(element: Element): boolean {
  const inputTypes = ['input', 'textarea', 'select']
  const tagName = element.tagName.toLowerCase()

  if (inputTypes.includes(tagName)) {
    return true
  }

  // 检查是否为可编辑元素
  if (element.getAttribute('contenteditable') === 'true') {
    return true
  }

  // 检查是否有inputmode属性（移动端数字输入框常用）
  if (element.getAttribute('inputmode')) {
    return true
  }

  // 检查是否为Ant Design Mobile Stepper组件的输入框
  const className = element.className || ''
  if (
    className.includes('adm-stepper-input') ||
    className.includes('adm-input') ||
    className.includes('stepper') ||
    className.includes('quantity')
  ) {
    return true
  }

  // 检查父元素是否为stepper相关组件
  const parent = element.parentElement
  if (parent) {
    const parentClassName = parent.className || ''
    if (
      parentClassName.includes('adm-stepper') ||
      parentClassName.includes('custom-stepper') ||
      parentClassName.includes('quantity-selector')
    ) {
      return tagName === 'input'
    }
  }

  return false
}

/**
 * Vaul风格的键盘修复Hook
 * 基于vaul的repositionInputs实现，专门处理iOS键盘弹出问题
 */
export const useVaulKeyboardFix = (
  drawerRef: React.RefObject<HTMLElement>,
  repositionInputs: boolean = true,
) => {
  const keyboardIsOpen = useRef(false)
  const previousDiffFromInitial = useRef(0)
  const initialDrawerHeight = useRef(0)

  useEffect(() => {
    function onVisualViewportChange() {
      if (!drawerRef.current || !repositionInputs) return

      const focusedElement = document.activeElement as HTMLElement
      const isInputFocused = isInput(focusedElement)

      // 添加调试信息（仅在开发环境）
      if (process.env.NODE_ENV === 'development' && isInputFocused) {
        console.log('键盘修复: 检测到输入框焦点', {
          tagName: focusedElement.tagName,
          className: focusedElement.className,
          inputmode: focusedElement.getAttribute('inputmode'),
        })
      }

      if (isInputFocused || keyboardIsOpen.current) {
        const visualViewportHeight = window.visualViewport?.height || 0
        const totalHeight = window.innerHeight
        // 这是键盘的高度
        const diffFromInitial = totalHeight - visualViewportHeight
        const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0
        // 只有当抽屉足够高时才调整高度
        const isTallEnough = drawerHeight > totalHeight * 0.8

        if (!initialDrawerHeight.current) {
          initialDrawerHeight.current = drawerHeight
        }
        const offsetFromTop = drawerRef.current.getBoundingClientRect().top

        // visualViewport高度可能因为键盘的细微变化而改变。检查高度变化是否超过60像素，以确保键盘真的改变了其打开状态。
        if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {
          keyboardIsOpen.current = !keyboardIsOpen.current

          // 添加调试信息（仅在开发环境）
          if (process.env.NODE_ENV === 'development') {
            console.log('键盘修复: 键盘状态变化', {
              keyboardIsOpen: keyboardIsOpen.current,
              diffFromInitial,
              visualViewportHeight,
              totalHeight,
            })
          }
        }

        previousDiffFromInitial.current = diffFromInitial

        // 当抽屉高度大于可视视口或键盘打开时，我们需要调整高度
        if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {
          const height = drawerRef.current.getBoundingClientRect().height
          let newDrawerHeight = height

          if (height > visualViewportHeight) {
            newDrawerHeight = visualViewportHeight - (isTallEnough ? offsetFromTop : 16) // 16px为顶部偏移
          }

          // 设置新的高度
          const finalHeight = Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)
          drawerRef.current.style.height = `${finalHeight}px`
        } else {
          // 恢复初始高度
          drawerRef.current.style.height = `${initialDrawerHeight.current}px`
        }

        // 调整底部位置以避免键盘遮挡
        drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`
      }
    }

    // 添加focusin和focusout事件监听，更好地处理输入框焦点变化
    function onFocusIn(event: FocusEvent) {
      const target = event.target as HTMLElement
      if (isInput(target)) {
        // 延迟执行，确保键盘已经弹出
        setTimeout(() => {
          onVisualViewportChange()
        }, 300)
      }
    }

    function onFocusOut() {
      // 输入框失去焦点时，延迟重置状态
      setTimeout(() => {
        keyboardIsOpen.current = false
        onVisualViewportChange()
      }, 300)
    }

    window.visualViewport?.addEventListener('resize', onVisualViewportChange)
    document.addEventListener('focusin', onFocusIn)
    document.addEventListener('focusout', onFocusOut)

    return () => {
      window.visualViewport?.removeEventListener('resize', onVisualViewportChange)
      document.removeEventListener('focusin', onFocusIn)
      document.removeEventListener('focusout', onFocusOut)
    }
  }, [drawerRef, repositionInputs])

  return {
    keyboardIsOpen: keyboardIsOpen.current,
  }
}
