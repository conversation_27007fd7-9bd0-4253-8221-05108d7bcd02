'use client'
import { useEffect, useRef } from 'react'

/**
 * 检测是否为输入元素
 */
function isInput(element: Element): boolean {
  const inputTypes = ['input', 'textarea', 'select']
  const tagName = element.tagName.toLowerCase()

  if (inputTypes.includes(tagName)) {
    return true
  }

  // 检查是否为可编辑元素
  if (element.getAttribute('contenteditable') === 'true') {
    return true
  }

  return false
}

/**
 * Vaul风格的键盘修复Hook
 * 基于vaul的repositionInputs实现，专门处理iOS键盘弹出问题
 */
export const useVaulKeyboardFix = (
  drawerRef: React.RefObject<HTMLElement>,
  repositionInputs: boolean = true,
) => {
  const keyboardIsOpen = useRef(false)
  const previousDiffFromInitial = useRef(0)
  const initialDrawerHeight = useRef(0)

  useEffect(() => {
    function onVisualViewportChange() {
      if (!drawerRef.current || !repositionInputs) return

      const focusedElement = document.activeElement as HTMLElement
      if (isInput(focusedElement) || keyboardIsOpen.current) {
        const visualViewportHeight = window.visualViewport?.height || 0
        const totalHeight = window.innerHeight
        // 这是键盘的高度
        const diffFromInitial = totalHeight - visualViewportHeight
        const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0
        // 只有当抽屉足够高时才调整高度
        const isTallEnough = drawerHeight > totalHeight * 0.8

        if (!initialDrawerHeight.current) {
          initialDrawerHeight.current = drawerHeight
        }
        const offsetFromTop = drawerRef.current.getBoundingClientRect().top

        // visualViewport高度可能因为键盘的细微变化而改变。检查高度变化是否超过60像素，以确保键盘真的改变了其打开状态。
        if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {
          keyboardIsOpen.current = !keyboardIsOpen.current
        }

        previousDiffFromInitial.current = diffFromInitial

        // 当抽屉高度大于可视视口或键盘打开时，我们需要调整高度
        if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {
          const height = drawerRef.current.getBoundingClientRect().height
          let newDrawerHeight = height

          if (height > visualViewportHeight) {
            newDrawerHeight = visualViewportHeight - (isTallEnough ? offsetFromTop : 16) // 16px为顶部偏移
          }

          // 设置新的高度
          const finalHeight = Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)
          drawerRef.current.style.height = `${finalHeight}px`
        } else {
          // 恢复初始高度
          drawerRef.current.style.height = `${initialDrawerHeight.current}px`
        }

        // 调整底部位置以避免键盘遮挡
        drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`
      }
    }

    window.visualViewport?.addEventListener('resize', onVisualViewportChange)
    return () => window.visualViewport?.removeEventListener('resize', onVisualViewportChange)
  }, [drawerRef, repositionInputs])

  return {
    keyboardIsOpen: keyboardIsOpen.current,
  }
}
